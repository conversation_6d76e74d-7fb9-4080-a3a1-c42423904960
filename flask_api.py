#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask API服务
提供情绪分类预测接口
"""

import os
import json
import torch
import numpy as np
from flask import Flask, request, jsonify
from flask_cors import CORS
from transformers import BertForSequenceClassification, BertTokenizer
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SentimentPredictor:
    """情绪分类预测器"""

    def __init__(self, model_path="./sentiment_model"):
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.label_names = None
        self.max_length = 128
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 加载模型
        self.load_model()

    def load_model(self):
        """加载训练好的模型和分词器"""
        try:
            logger.info(f"正在从 {self.model_path} 加载模型...")

            # 检查模型文件是否存在
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型目录不存在: {self.model_path}")

            # 加载标签映射
            label_mapping_path = os.path.join(self.model_path, "label_mapping.json")
            if os.path.exists(label_mapping_path):
                with open(label_mapping_path, "r", encoding="utf-8") as f:
                    label_mapping = json.load(f)
                self.label_names = label_mapping["label_names"]
                num_labels = label_mapping["num_labels"]
            else:
                # 默认标签（如果没有标签映射文件）
                self.label_names = ["anger", "disgust", "fear", "happiness", "like", "sadness", "surprise"]
                num_labels = len(self.label_names)

            # 加载模型
            self.model = BertForSequenceClassification.from_pretrained(
                self.model_path,
                num_labels=num_labels,
                local_files_only=True
            )
            self.model.to(self.device)
            self.model.eval()

            # 加载分词器
            self.tokenizer = BertTokenizer.from_pretrained(
                self.model_path,
                local_files_only=True
            )

            logger.info("模型加载成功!")
            logger.info(f"设备: {self.device}")
            logger.info(f"标签: {self.label_names}")

        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise e

    def predict(self, text):
        """预测单个文本的情绪"""
        try:
            # 文本预处理
            text = str(text).strip()
            if not text:
                return {"error": "输入文本为空"}

            # 分词编码
            encoding = self.tokenizer(
                text,
                truncation=True,
                padding="max_length",
                max_length=self.max_length,
                return_tensors="pt"
            )

            # 移动到设备
            input_ids = encoding["input_ids"].to(self.device)
            attention_mask = encoding["attention_mask"].to(self.device)

            # 预测
            with torch.no_grad():
                outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
                logits = outputs.logits
                probabilities = torch.softmax(logits, dim=-1)
                predicted_class = torch.argmax(logits, dim=-1).item()

            # 获取所有类别的概率
            probs = probabilities.cpu().numpy()[0]

            # 构建结果
            result = {
                "text": text,
                "predicted_label": self.label_names[predicted_class],
                "confidence": float(probs[predicted_class]),
                "all_probabilities": {
                    label: float(prob) for label, prob in zip(self.label_names, probs)
                }
            }

            return result

        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {"error": f"预测失败: {str(e)}"}

    def predict_batch(self, texts):
        """批量预测"""
        results = []
        for text in texts:
            result = self.predict(text)
            results.append(result)
        return results

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局预测器实例（只加载一次）
predictor = None

def initialize_predictor():
    """初始化预测器"""
    global predictor
    if predictor is None:
        try:
            predictor = SentimentPredictor()
            logger.info("预测器初始化成功")
        except Exception as e:
            logger.error(f"预测器初始化失败: {e}")
            raise e

@app.route("/", methods=["GET"])
def home():
    """首页"""
    return jsonify({
        "message": "中文情绪分类API服务",
        "version": "1.0",
        "endpoints": {
            "POST /predict": "单个文本情绪预测",
            "POST /predict_batch": "批量文本情绪预测",
            "GET /health": "健康检查"
        }
    })

@app.route("/health", methods=["GET"])
def health_check():
    """健康检查"""
    global predictor
    if predictor is None:
        return jsonify({"status": "error", "message": "模型未加载"}), 500

    return jsonify({
        "status": "healthy",
        "model_loaded": True,
        "device": str(predictor.device),
        "labels": predictor.label_names
    })

@app.route("/predict", methods=["POST"])
def predict():
    """单个文本情绪预测"""
    global predictor

    # 检查模型是否已加载
    if predictor is None:
        return jsonify({"error": "模型未加载"}), 500

    try:
        # 获取请求数据
        data = request.get_json()

        if not data or "text" not in data:
            return jsonify({"error": "请求格式错误，需要包含'text'字段"}), 400

        text = data["text"]

        # 预测
        result = predictor.predict(text)

        # 检查是否有错误
        if "error" in result:
            return jsonify(result), 400

        return jsonify(result)

    except Exception as e:
        logger.error(f"预测接口错误: {e}")
        return jsonify({"error": f"服务器内部错误: {str(e)}"}), 500

@app.route("/predict_batch", methods=["POST"])
def predict_batch():
    """批量文本情绪预测"""
    global predictor

    # 检查模型是否已加载
    if predictor is None:
        return jsonify({"error": "模型未加载"}), 500

    try:
        # 获取请求数据
        data = request.get_json()

        if not data or "texts" not in data:
            return jsonify({"error": "请求格式错误，需要包含'texts'字段（文本列表）"}), 400

        texts = data["texts"]

        if not isinstance(texts, list):
            return jsonify({"error": "'texts'字段必须是列表"}), 400

        if len(texts) > 100:  # 限制批量大小
            return jsonify({"error": "批量预测最多支持100个文本"}), 400

        # 批量预测
        results = predictor.predict_batch(texts)

        return jsonify({"results": results})

    except Exception as e:
        logger.error(f"批量预测接口错误: {e}")
        return jsonify({"error": f"服务器内部错误: {str(e)}"}), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({"error": "接口不存在"}), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({"error": "服务器内部错误"}), 500

if __name__ == "__main__":
    try:
        # 初始化预测器
        initialize_predictor()

        # 启动Flask应用
        logger.info("启动Flask API服务...")
        app.run(
            host="0.0.0.0",
            port=5000,
            debug=False,
            threaded=True
        )

    except Exception as e:
        logger.error(f"启动失败: {e}")
        print(f"错误: {e}")
        print("请确保模型已训练完成并保存在 ./sentiment_model 目录中")