#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例脚本
演示如何使用中文情绪分类系统
"""

import requests
import json
import time

def test_api_prediction():
    """测试API预测功能"""
    base_url = "http://localhost:5000"

    # 测试文本
    test_texts = [
        "今天天气真好，心情特别愉快！",
        "这件事让我非常生气，太不公平了！",
        "看到这个消息我很惊讶，没想到会这样。",
        "感到很害怕，不知道该怎么办才好。",
        "这个电影真的很棒，我很喜欢！",
        "心情很低落，感觉什么都不顺利。",
        "这个味道真恶心，让人反胃。"
    ]

    print("=" * 60)
    print("中文情绪分类系统 - API 测试")
    print("=" * 60)

    # 检查服务状态
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_info = response.json()
            print("✓ API 服务正常运行")
            print(f"  设备: {health_info.get('device', 'unknown')}")
            print(f"  支持标签: {', '.join(health_info.get('labels', []))}")
        else:
            print("✗ API 服务异常")
            return
    except requests.exceptions.RequestException:
        print("✗ 无法连接到API服务")
        print("请确保已启动Flask服务: python flask_api.py")
        return

    print("\n" + "-" * 60)
    print("单个文本预测测试:")
    print("-" * 60)

    # 单个预测测试
    for i, text in enumerate(test_texts, 1):
        try:
            response = requests.post(
                f"{base_url}/predict",
                json={"text": text},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                print(f"\n{i}. 文本: {text}")
                print(f"   预测: {result['predicted_label']} (置信度: {result['confidence']:.3f})")

                # 显示前3个最高概率的标签
                probs = result['all_probabilities']
                sorted_probs = sorted(probs.items(), key=lambda x: x[1], reverse=True)[:3]
                print(f"   概率分布: {', '.join([f'{label}({prob:.3f})' for label, prob in sorted_probs])}")
            else:
                print(f"✗ 预测失败: {response.status_code}")

        except requests.exceptions.RequestException as e:
            print(f"✗ 请求失败: {e}")

    print("\n" + "-" * 60)
    print("批量预测测试:")
    print("-" * 60)

    # 批量预测测试
    try:
        batch_texts = test_texts[:3]  # 取前3个文本进行批量测试
        response = requests.post(
            f"{base_url}/predict_batch",
            json={"texts": batch_texts},
            timeout=15
        )

        if response.status_code == 200:
            results = response.json()['results']
            print("✓ 批量预测成功:")
            for i, result in enumerate(results, 1):
                if 'error' not in result:
                    print(f"  {i}. {result['text'][:30]}... -> {result['predicted_label']} ({result['confidence']:.3f})")
                else:
                    print(f"  {i}. 预测失败: {result['error']}")
        else:
            print(f"✗ 批量预测失败: {response.status_code}")

    except requests.exceptions.RequestException as e:
        print(f"✗ 批量预测请求失败: {e}")

    print("\n" + "=" * 60)
    print("测试完成!")

def demo_training():
    """演示模型训练过程"""
    print("=" * 60)
    print("模型训练演示")
    print("=" * 60)
    print("注意: 完整训练需要较长时间，这里只是演示流程")

    try:
        from train_model import SentimentTrainer

        # 创建训练器
        trainer = SentimentTrainer(
            csv_file="emotion_data.csv",
            model_save_path="./sentiment_model_demo",
            pretrained_model_path="chinese_L-12_H-768_A-12"
        )

        print("✓ 训练器创建成功")
        print("如需进行完整训练，请运行: python train_model.py")

    except Exception as e:
        print(f"✗ 训练器创建失败: {e}")

def main():
    """主函数"""
    print("中文情绪分类系统使用示例")
    print("请选择要运行的示例:")
    print("1. API预测测试 (需要先启动Flask服务)")
    print("2. 训练流程演示")
    print("3. 退出")

    while True:
        choice = input("\n请输入选择 (1-3): ").strip()

        if choice == "1":
            test_api_prediction()
            break
        elif choice == "2":
            demo_training()
            break
        elif choice == "3":
            print("退出程序")
            break
        else:
            print("无效选择，请输入 1-3")

if __name__ == "__main__":
    main()