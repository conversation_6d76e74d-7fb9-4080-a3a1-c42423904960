#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据加载与预处理模块
使用BertTokenizer对文本进行分词编码
"""

import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import BertTokenizer
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import numpy as np
import os

class EmotionDataset(Dataset):
    """情绪分类数据集类"""

    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]

        # 使用tokenizer编码文本
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding="max_length",
            max_length=self.max_length,
            return_tensors="pt"
        )

        return {
            "input_ids": encoding["input_ids"].flatten(),
            "attention_mask": encoding["attention_mask"].flatten(),
            "labels": torch.tensor(label, dtype=torch.long)
        }

class DataProcessor:
    """数据处理器"""

    def __init__(self, csv_file, tokenizer_path="bert-base-chinese", max_length=128):
        self.csv_file = csv_file
        self.tokenizer_path = tokenizer_path
        self.max_length = max_length
        self.label_encoder = LabelEncoder()
        self.tokenizer = None
        self.num_labels = 0

    def load_data(self):
        """加载CSV数据"""
        print(f"正在加载数据: {self.csv_file}")
        df = pd.read_csv(self.csv_file, encoding="utf-8")

        # 检查数据
        print(f"数据形状: {df.shape}")
        print(f"标签分布:")
        print(df["label"].value_counts())

        return df

    def prepare_tokenizer(self):
        """准备分词器"""
        print(f"正在加载分词器: {self.tokenizer_path}")

        # 尝试从本地中文BERT模型加载
        try:
            vocab_file = "chinese_L-12_H-768_A-12/vocab.txt"
            if os.path.exists(vocab_file):
                self.tokenizer = BertTokenizer.from_pretrained(
                    "chinese_L-12_H-768_A-12",
                    local_files_only=True
                )
                print("成功加载本地中文BERT分词器")
            else:
                # 使用在线模型
                self.tokenizer = BertTokenizer.from_pretrained("bert-base-chinese")
                print("成功加载在线中文BERT分词器")
        except Exception as e:
            print(f"加载分词器失败: {e}")
            # 备用方案
            self.tokenizer = BertTokenizer.from_pretrained("bert-base-chinese")
            print("使用备用分词器")

        return self.tokenizer

    def encode_labels(self, labels):
        """编码标签"""
        encoded_labels = self.label_encoder.fit_transform(labels)
        self.num_labels = len(self.label_encoder.classes_)

        print(f"标签编码映射:")
        for i, label in enumerate(self.label_encoder.classes_):
            print(f"  {label}: {i}")

        return encoded_labels

    def prepare_datasets(self, test_size=0.2, val_size=0.1, random_state=42):
        """准备训练、验证和测试数据集"""
        # 加载数据
        df = self.load_data()

        # 准备分词器
        self.prepare_tokenizer()

        # 编码标签
        encoded_labels = self.encode_labels(df["label"])

        # 分割数据
        X_temp, X_test, y_temp, y_test = train_test_split(
            df["text"].values, encoded_labels,
            test_size=test_size, random_state=random_state, stratify=encoded_labels
        )

        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp,
            test_size=val_size/(1-test_size), random_state=random_state, stratify=y_temp
        )

        print(f"数据分割结果:")
        print(f"  训练集: {len(X_train)} 样本")
        print(f"  验证集: {len(X_val)} 样本")
        print(f"  测试集: {len(X_test)} 样本")

        # 创建数据集
        train_dataset = EmotionDataset(X_train, y_train, self.tokenizer, self.max_length)
        val_dataset = EmotionDataset(X_val, y_val, self.tokenizer, self.max_length)
        test_dataset = EmotionDataset(X_test, y_test, self.tokenizer, self.max_length)

        return train_dataset, val_dataset, test_dataset

    def get_label_names(self):
        """获取标签名称"""
        return self.label_encoder.classes_.tolist()

    def decode_label(self, encoded_label):
        """解码标签"""
        return self.label_encoder.inverse_transform([encoded_label])[0]

def main():
    """测试数据加载"""
    processor = DataProcessor("emotion_data.csv")
    train_dataset, val_dataset, test_dataset = processor.prepare_datasets()

    print(f"\n数据集创建完成:")
    print(f"  训练集大小: {len(train_dataset)}")
    print(f"  验证集大小: {len(val_dataset)}")
    print(f"  测试集大小: {len(test_dataset)}")
    print(f"  标签数量: {processor.num_labels}")

    # 测试一个样本
    sample = train_dataset[0]
    print(f"\n样本测试:")
    print(f"  input_ids shape: {sample['input_ids'].shape}")
    print(f"  attention_mask shape: {sample['attention_mask'].shape}")
    print(f"  label: {sample['labels']}")

if __name__ == "__main__":
    main()