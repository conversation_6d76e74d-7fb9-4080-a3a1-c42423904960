{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "Predicting Movie Reviews with BERT on TF Hub.ipynb", "version": "0.3.2", "provenance": [], "collapsed_sections": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"metadata": {"id": "j0a4mTk9o1Qg", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["# Copyright 2019 Google Inc.\n", "\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."], "execution_count": 0, "outputs": []}, {"metadata": {"id": "dCpvgG0vwXAZ", "colab_type": "text"}, "cell_type": "markdown", "source": ["#Predicting Movie Review Sentiment with BERT on TF Hub"]}, {"metadata": {"id": "xiYrZKaHwV81", "colab_type": "text"}, "cell_type": "markdown", "source": ["If you’ve been following Natural Language Processing over the past year, you’ve probably heard of BERT: Bidirectional Encoder Representations from Transformers. It’s a neural network architecture designed by Google researchers that’s totally transformed what’s state-of-the-art for NLP tasks, like text classification, translation, summarization, and question answering.\n", "\n", "Now that BERT's been added to [TF Hub](https://www.tensorflow.org/hub) as a loadable module, it's easy(ish) to add into existing Tensorflow text pipelines. In an existing pipeline, BERT can replace text embedding layers like ELMO and GloVE. Alternatively, [finetuning](http://wiki.fast.ai/index.php/Fine_tuning) BERT can provide both an accuracy boost and faster training time in many cases.\n", "\n", "Here, we'll train a model to predict whether an IMDB movie review is positive or negative using BERT in Tensorflow with tf hub. Some code was adapted from [this colab notebook](https://colab.sandbox.google.com/github/tensorflow/tpu/blob/master/tools/colab/bert_finetuning_with_cloud_tpus.ipynb). Let's get started!"]}, {"metadata": {"id": "hsZvic2YxnTz", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["from sklearn.model_selection import train_test_split\n", "import pandas as pd\n", "import tensorflow as tf\n", "import tensorflow_hub as hub\n", "from datetime import datetime"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "cp5wfXDx5SPH", "colab_type": "text"}, "cell_type": "markdown", "source": ["In addition to the standard libraries we imported above, we'll need to install BERT's python package."]}, {"metadata": {"id": "jviywGyWyKsA", "colab_type": "code", "outputId": "166f3005-d219-404f-b201-2a0b75480360", "colab": {"base_uri": "https://localhost:8080/", "height": 51}}, "cell_type": "code", "source": ["!pip install bert-tensorflow"], "execution_count": 38, "outputs": [{"output_type": "stream", "text": ["Requirement already satisfied: bert-tensorflow in /usr/local/lib/python3.6/dist-packages (1.0.1)\n", "Requirement already satisfied: six in /usr/local/lib/python3.6/dist-packages (from bert-tensorflow) (1.11.0)\n"], "name": "stdout"}]}, {"metadata": {"id": "hhbGEfwgdEtw", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["import bert\n", "from bert import run_classifier\n", "from bert import optimization\n", "from bert import tokenization"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "KVB3eOcjxxm1", "colab_type": "text"}, "cell_type": "markdown", "source": ["Below, we'll set an output directory location to store our model output and checkpoints. This can be a local directory, in which case you'd set OUTPUT_DIR to the name of the directory you'd like to create. If you're running this code in Google's hosted Colab, the directory won't persist after the Colab session ends.\n", "\n", "Alternatively, if you're a GCP user, you can store output in a GCP bucket. To do that, set a directory name in OUTPUT_DIR and the name of the GCP bucket in the BUCKET field.\n", "\n", "Set DO_DELETE to rewrite the OUTPUT_DIR if it exists. Otherwise, Tensorflow will load existing model checkpoints from that directory (if they exist)."]}, {"metadata": {"id": "US_EAnICvP7f", "colab_type": "code", "outputId": "7780a032-31d4-4794-e6aa-664a5d2ae7dd", "cellView": "form", "colab": {"base_uri": "https://localhost:8080/", "height": 34}}, "cell_type": "code", "source": ["# Set the output directory for saving model file\n", "# Optionally, set a GCP bucket location\n", "\n", "OUTPUT_DIR = 'OUTPUT_DIR_NAME'#@param {type:\"string\"}\n", "#@markdown Whether or not to clear/delete the directory and create a new one\n", "DO_DELETE = False #@param {type:\"boolean\"}\n", "#@markdown Set USE_BUCKET and BUCKET if you want to (optionally) store model output on GCP bucket.\n", "USE_BUCKET = True #@param {type:\"boolean\"}\n", "BUCKET = 'BUCKET_NAME' #@param {type:\"string\"}\n", "\n", "if USE_BUCKET:\n", "  OUTPUT_DIR = 'gs://{}/{}'.format(BUCKET, OUTPUT_DIR)\n", "  from google.colab import auth\n", "  auth.authenticate_user()\n", "\n", "if DO_DELETE:\n", "  try:\n", "    tf.gfile.DeleteRecursively(OUTPUT_DIR)\n", "  except:\n", "    # Doesn't matter if the directory didn't exist\n", "    pass\n", "tf.gfile.MakeDirs(OUTPUT_DIR)\n", "print('***** Model output directory: {} *****'.format(OUTPUT_DIR))\n"], "execution_count": 40, "outputs": [{"output_type": "stream", "text": ["***** Model output directory: gs://bert-tfhub/aclImdb_v1 *****\n"], "name": "stdout"}]}, {"metadata": {"id": "pmFYvkylMwXn", "colab_type": "text"}, "cell_type": "markdown", "source": ["#Data"]}, {"metadata": {"id": "MC_w8SRqN0fr", "colab_type": "text"}, "cell_type": "markdown", "source": ["First, let's download the dataset, hosted by Stanford. The code below, which downloads, extracts, and imports the IMDB Large Movie Review Dataset, is borrowed from [this Tensorflow tutorial](https://www.tensorflow.org/hub/tutorials/text_classification_with_tf_hub)."]}, {"metadata": {"id": "fom_ff20gyy6", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["from tensorflow import keras\n", "import os\n", "import re\n", "\n", "# Load all files from a directory in a DataFrame.\n", "def load_directory_data(directory):\n", "  data = {}\n", "  data[\"sentence\"] = []\n", "  data[\"sentiment\"] = []\n", "  for file_path in os.listdir(directory):\n", "    with tf.gfile.GFile(os.path.join(directory, file_path), \"r\") as f:\n", "      data[\"sentence\"].append(f.read())\n", "      data[\"sentiment\"].append(re.match(\"\\d+_(\\d+)\\.txt\", file_path).group(1))\n", "  return pd.DataFrame.from_dict(data)\n", "\n", "# Merge positive and negative examples, add a polarity column and shuffle.\n", "def load_dataset(directory):\n", "  pos_df = load_directory_data(os.path.join(directory, \"pos\"))\n", "  neg_df = load_directory_data(os.path.join(directory, \"neg\"))\n", "  pos_df[\"polarity\"] = 1\n", "  neg_df[\"polarity\"] = 0\n", "  return pd.concat([pos_df, neg_df]).sample(frac=1).reset_index(drop=True)\n", "\n", "# Download and process the dataset files.\n", "def download_and_load_datasets(force_download=False):\n", "  dataset = tf.keras.utils.get_file(\n", "      fname=\"aclImdb.tar.gz\", \n", "      origin=\"http://ai.stanford.edu/~amaas/data/sentiment/aclImdb_v1.tar.gz\", \n", "      extract=True)\n", "  \n", "  train_df = load_dataset(os.path.join(os.path.dirname(dataset), \n", "                                       \"aclImdb\", \"train\"))\n", "  test_df = load_dataset(os.path.join(os.path.dirname(dataset), \n", "                                      \"aclImdb\", \"test\"))\n", "  \n", "  return train_df, test_df\n"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "2abfwdn-g135", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["train, test = download_and_load_datasets()"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "XA8WHJgzhIZf", "colab_type": "text"}, "cell_type": "markdown", "source": ["To keep training fast, we'll take a sample of 5000 train and test examples, respectively."]}, {"metadata": {"id": "lw_F488eixTV", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["train = train.sample(5000)\n", "test = test.sample(5000)"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "prRQM8pDi8xI", "colab_type": "code", "outputId": "34445cb8-2be0-4379-fdbc-7794091f6049", "colab": {"base_uri": "https://localhost:8080/", "height": 34}}, "cell_type": "code", "source": ["train.columns"], "execution_count": 44, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Index(['sentence', 'sentiment', 'polarity'], dtype='object')"]}, "metadata": {"tags": []}, "execution_count": 44}]}, {"metadata": {"id": "sfRnHSz3iSXz", "colab_type": "text"}, "cell_type": "markdown", "source": ["For us, our input data is the 'sentence' column and our label is the 'polarity' column (0, 1 for negative and positive, respecitvely)"]}, {"metadata": {"id": "IuMOGwFui4it", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["DATA_COLUMN = 'sentence'\n", "LABEL_COLUMN = 'polarity'\n", "# label_list is the list of labels, i.e. True, False or 0, 1 or 'dog', 'cat'\n", "label_list = [0, 1]"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "V399W0rqNJ-Z", "colab_type": "text"}, "cell_type": "markdown", "source": ["#Data Preprocessing\n", "We'll need to transform our data into a format BERT understands. This involves two steps. First, we create  `InputExample`'s using the constructor provided in the BERT library.\n", "\n", "- `text_a` is the text we want to classify, which in this case, is the `Request` field in our Dataframe. \n", "- `text_b` is used if we're training a model to understand the relationship between sentences (i.e. is `text_b` a translation of `text_a`? Is `text_b` an answer to the question asked by `text_a`?). This doesn't apply to our task, so we can leave `text_b` blank.\n", "- `label` is the label for our example, i.e. True, False"]}, {"metadata": {"id": "p9gEt5SmM6i6", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["# Use the InputExample class from BERT's run_classifier code to create examples from the data\n", "train_InputExamples = train.apply(lambda x: bert.run_classifier.InputExample(guid=None, # Globally unique ID for bookkeeping, unused in this example\n", "                                                                   text_a = x[DATA_COLUMN], \n", "                                                                   text_b = None, \n", "                                                                   label = x[LABEL_COLUMN]), axis = 1)\n", "\n", "test_InputExamples = test.apply(lambda x: bert.run_classifier.InputExample(guid=None, \n", "                                                                   text_a = x[DATA_COLUMN], \n", "                                                                   text_b = None, \n", "                                                                   label = x[LABEL_COLUMN]), axis = 1)"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "SCZWZtKxObjh", "colab_type": "text"}, "cell_type": "markdown", "source": ["Next, we need to preprocess our data so that it matches the data BERT was trained on. For this, we'll need to do a couple of things (but don't worry--this is also included in the Python library):\n", "\n", "\n", "1. Lowercase our text (if we're using a BERT lowercase model)\n", "2. Tokenize it (i.e. \"sally says hi\" -> [\"sally\", \"says\", \"hi\"])\n", "3. Break words into WordPieces (i.e. \"calling\" -> [\"call\", \"##ing\"])\n", "4. Map our words to indexes using a vocab file that BERT provides\n", "5. Add special \"CLS\" and \"SEP\" tokens (see the [readme](https://github.com/google-research/bert))\n", "6. Append \"index\" and \"segment\" tokens to each input (see the [BERT paper](https://arxiv.org/pdf/1810.04805.pdf))\n", "\n", "Happily, we don't have to worry about most of these details.\n", "\n", "\n"]}, {"metadata": {"id": "qMWiDtpyQSoU", "colab_type": "text"}, "cell_type": "markdown", "source": ["To start, we'll need to load a vocabulary file and lowercasing information directly from the BERT tf hub module:"]}, {"metadata": {"id": "IhJSe0QHNG7U", "colab_type": "code", "outputId": "20b28cc7-3cb3-4ce6-bfff-a7847ce3bbaa", "colab": {"base_uri": "https://localhost:8080/", "height": 34}}, "cell_type": "code", "source": ["# This is a path to an uncased (all lowercase) version of BERT\n", "BERT_MODEL_HUB = \"https://tfhub.dev/google/bert_uncased_L-12_H-768_A-12/1\"\n", "\n", "def create_tokenizer_from_hub_module():\n", "  \"\"\"Get the vocab file and casing info from the Hub module.\"\"\"\n", "  with tf.Graph().as_default():\n", "    bert_module = hub.Module(BERT_MODEL_HUB)\n", "    tokenization_info = bert_module(signature=\"tokenization_info\", as_dict=True)\n", "    with tf.Session() as sess:\n", "      vocab_file, do_lower_case = sess.run([tokenization_info[\"vocab_file\"],\n", "                                            tokenization_info[\"do_lower_case\"]])\n", "      \n", "  return bert.tokenization.FullTokenizer(\n", "      vocab_file=vocab_file, do_lower_case=do_lower_case)\n", "\n", "tokenizer = create_tokenizer_from_hub_module()"], "execution_count": 47, "outputs": [{"output_type": "stream", "text": ["INFO:tensorflow:Saver not created because there are no variables in the graph to restore\n"], "name": "stdout"}]}, {"metadata": {"id": "z4oFkhpZBDKm", "colab_type": "text"}, "cell_type": "markdown", "source": ["Great--we just learned that the BERT model we're using expects lowercase data (that's what stored in tokenization_info[\"do_lower_case\"]) and we also loaded BERT's vocab file. We also created a tokenizer, which breaks words into word pieces:"]}, {"metadata": {"id": "dsBo6RCtQmwx", "colab_type": "code", "outputId": "9af8c917-90ec-4fe9-897b-79dc89ca88e1", "colab": {"base_uri": "https://localhost:8080/", "height": 221}}, "cell_type": "code", "source": ["tokenizer.tokenize(\"This here's an example of using the BERT tokenizer\")"], "execution_count": 48, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['this',\n", " 'here',\n", " \"'\",\n", " 's',\n", " 'an',\n", " 'example',\n", " 'of',\n", " 'using',\n", " 'the',\n", " 'bert',\n", " 'token',\n", " '##izer']"]}, "metadata": {"tags": []}, "execution_count": 48}]}, {"metadata": {"id": "0OEzfFIt6GIc", "colab_type": "text"}, "cell_type": "markdown", "source": ["Using our tokenizer, we'll call `run_classifier.convert_examples_to_features` on our InputExamples to convert them into features BERT understands."]}, {"metadata": {"id": "LL5W8gEGRTAf", "colab_type": "code", "outputId": "65001dda-155b-48fc-b5fc-1e4cabc8dfbf", "colab": {"base_uri": "https://localhost:8080/", "height": 1261}}, "cell_type": "code", "source": ["# We'll set sequences to be at most 128 tokens long.\n", "MAX_SEQ_LENGTH = 128\n", "# Convert our train and test features to InputFeatures that BERT understands.\n", "train_features = bert.run_classifier.convert_examples_to_features(train_InputExamples, label_list, MAX_SEQ_LENGTH, tokenizer)\n", "test_features = bert.run_classifier.convert_examples_to_features(test_InputExamples, label_list, MAX_SEQ_LENGTH, tokenizer)"], "execution_count": 49, "outputs": [{"output_type": "stream", "text": ["INFO:tensorflow:Writing example 0 of 5000\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: None\n", "INFO:tensorflow:tokens: [CLS] i ' m watching this on the sci - fi channel right now . it ' s so horrible i can ' t stop watching it ! i ' m a video ##grapher and this movie makes me sad . i feel bad for anyone associated with this movie . some of the camera work is good . most is very questionable . there are a few decent actors in the flick . too bad they ' re surrounded by what must have been the director ' s relatives . that ' s the only way they could have been qualified to be in a movie ! music was a little better than the acting . if you get around to watching this i hope it [SEP]\n", "INFO:tensorflow:input_ids: 101 1045 1005 1049 ************** 1996 16596 1011 10882 ************** 1012 ************** ************** ************** ************** 999 1045 1005 1049 1037 2678 18657 1998 ************** ************** 1045 ************** ************** ************** ************** ************** ************** 2003 2200 21068 1012 ************** 2261 11519 5889 1999 1996 17312 1012 2205 2919 2027 1005 2128 5129 2011 2054 2442 2031 2042 1996 2472 1005 1055 9064 1012 2008 1005 1055 1996 2069 2126 2027 2071 2031 2042 4591 2000 2022 1999 1037 3185 999 2189 2001 1037 2210 2488 2084 1996 3772 1012 2065 2017 2131 2105 2000 3666 2023 1045 3246 2009 102\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 0 (id = 0)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: None\n", "INFO:tensorflow:tokens: [CLS] i have been a fan of pushing dai ##sies since the very beginning . it is wonderful ##ly thought up , and br<PERSON> <PERSON><PERSON> has the most remarkable ideas for this show . < br / > < br / > it is unbelievable on how much tv has been needing a creative , original show like pushing dai ##sies . it is a huge relief to see a show , that is unlike the rest , where as , if you compared it to some of the newer shows , such as scrub ##s and house , you would see the similarities , and it does get ted ##ious at moments to see shows so close in identity . < br / > < br [SEP]\n", "INFO:tensorflow:input_ids: 101 1045 ************** ************** 18765 14625 ************** ************** ************** ************** 1998 8527 12548 ************** ************** ************** 1026 ************** 1026 ************** 2009 2003 23653 ************** ************** 11303 1037 ************** ************** 18765 14625 1012 ************** 4121 4335 2000 2156 1037 2265 1010 2008 2003 4406 1996 2717 1010 2073 2004 1010 2065 2017 4102 2009 2000 ************** 10947 3065 1010 2107 2004 18157 2015 1998 2160 1010 2017 2052 2156 1996 12319 1010 1998 2009 2515 2131 6945 6313 2012 5312 2000 2156 3065 2061 2485 1999 4767 1012 1026 ************** 1026 7987 102\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 1 (id = 1)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: None\n", "INFO:tensorflow:tokens: [CLS] this movie starts out promising ##ly , with an early scene in which frank morgan advises against gary cooper ' s marriage to his daughter , anita louise . frank morgan , playing an una ##bas ##hed gold - digger , loudly complain ##s to cooper about his perceived pen ##ury at the hands of his family - including his daughter , anita louise . i am a fan of all 3 actors . frank morgan is ( to my mind ) a hollywood treasure , cooper a legend , and louise a very lovely , versatile and under - appreciated actress seldom seen in the leading role . i also have nothing against teresa wright , and while not blessed with great range , she [SEP]\n", "INFO:tensorflow:input_ids: 101 ************** 2041 10015 ************** ************** 1999 ************** 25453 ************** 1005 1055 ************** 2684 1010 12918 ************** ************** 2019 14477 22083 ************** 28661 1010 9928 17612 ************** ************** 7279 13098 ************** 1997 2010 2155 1011 2164 2010 2684 1010 12918 8227 1012 1045 2572 1037 5470 1997 2035 1017 5889 1012 3581 5253 2003 1006 2000 2026 2568 1007 1037 5365 8813 1010 6201 1037 5722 1010 1998 8227 1037 2200 8403 1010 22979 1998 2104 1011 12315 3883 15839 2464 1999 1996 2877 2535 1012 1045 2036 2031 2498 2114 12409 6119 1010 1998 2096 2025 10190 2007 2307 2846 1010 2016 102\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 0 (id = 0)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: None\n", "INFO:tensorflow:tokens: [CLS] i was over ##taken by the emotion . un ##for ##get ##table rendering of a wartime story which is unknown to most people . the performances were fault ##less and outstanding . [SEP]\n", "INFO:tensorflow:input_ids: 101 1045 2001 2058 25310 ************** 1012 4895 29278 18150 10880 14259 1997 1037 12498 ************** ************** ************** ************** ************** 1012 102 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 1 (id = 1)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: None\n", "INFO:tensorflow:tokens: [CLS] soldier blue is a movie with pre ##tension ##s : pre ##tension ##s to be some sort of profound statement on man ' s inhuman ##ity to man , on the white man ' s exploitation of and brutality towards indigenous peoples ; a biting , un ##fl ##in ##ching and sar ##don ##ic commentary on the horrors of vietnam . well , sorry , but it fails mis ##era ##bly to be any of those things . what soldier blue actually is is per ##nic ##ious , tri ##te , badly made , dish ##ones ##t rubbish . < br / > < br / > another reviewer here hit the nail on the head in saying that it appears to be a hybrid of [SEP]\n", "INFO:tensorflow:input_ids: 101 ************** 1037 ************** 29048 ************** 29048 ************** ************** 13769 ************** 1005 1055 29582 ************** 1010 ************** ************** 14427 1997 1998 24083 ************** 1025 1037 12344 1010 4895 10258 ************** 18906 ************** 2006 1996 22812 1997 ************** 1010 3374 1010 2021 2009 11896 28616 6906 6321 2000 2022 2151 1997 2216 2477 1012 2054 5268 2630 2941 2003 2003 2566 8713 6313 1010 13012 2618 1010 6649 2081 1010 9841 21821 2102 29132 1012 1026 ************** 1026 ************** 2178 12027 2182 2718 1996 13774 2006 1996 2132 1999 3038 2008 2009 3544 2000 2022 1037 8893 1997 102\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 0 (id = 0)\n", "INFO:tensorflow:Writing example 0 of 5000\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: None\n", "INFO:tensorflow:tokens: [CLS] i just watched this today on tv . it was on abc ' s sunday afternoon movie . < br / > < br / > this wasn ' t a very good movie , but for a low budget independent film like this , it was okay . there is some suspense in it , but there are so many bad qualities that really bring the movie down . the script is pretty lame , and the plot elements aren ' t very realistic , such as the way a 911 operator would laugh and hang up when someone is reporting a murder . i don ' t know what the writer was thinking when they came up with that idea , but it isn [SEP]\n", "INFO:tensorflow:input_ids: 101 1045 ************** ************** 1012 ************** ************** ************** 1012 1026 ************** 1026 ************** ************** 1056 1037 ************** 1010 ************** ************** ************** 1010 ************** 1012 ************** 23873 1999 ************** ************** 2116 2919 11647 ************** 1996 ************** 1996 5896 2003 3492 20342 1010 1998 1996 5436 3787 4995 1005 1056 2200 12689 1010 2107 2004 1996 2126 1037 19989 6872 2052 4756 1998 6865 2039 2043 2619 2003 7316 1037 4028 1012 1045 2123 1005 1056 2113 2054 1996 3213 2001 3241 2043 2027 2234 2039 2007 2008 2801 1010 2021 2009 3475 102\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 0 (id = 0)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: None\n", "INFO:tensorflow:tokens: [CLS] from hardly alien sounding lasers , to an elementary school style shuttle crash , \" night ##be ##ast \" is better classified as a far ##cic ##al mix of fake blood and bare chest . the almost pornographic style of the film seems to be a failed attempt to recover from a lack of co ##hesive or effective story . the acting however is not nearly as beast ##ly , many of the young , aspiring , actors ad ##mir ##ably showcase a hidden talent . particularly don lei ##fer ##t and jamie ze ##mare ##l , who shed a well needed sha ##rd of light on this otherwise terrible film . night ##be ##ast would have never shown up on set had he known the [SEP]\n", "INFO:tensorflow:input_ids: 101 ************** 9391 23965 1010 ************** 2082 2806 10382 ************** 2305 4783 14083 1000 ************** ************** 19053 ************** ************** ************** 1996 2471 26932 ************** ************** ************** ************** ************** 1997 2522 21579 2030 4621 2466 1012 1996 3772 2174 2003 2025 3053 2004 6841 2135 1010 2116 1997 1996 2402 1010 22344 1010 5889 4748 14503 8231 13398 1037 5023 5848 1012 3391 2123 26947 7512 2102 1998 6175 27838 24376 2140 1010 2040 8328 1037 2092 2734 21146 4103 1997 2422 2006 2023 4728 6659 2143 1012 2305 4783 14083 2052 2031 2196 3491 2039 2006 2275 2018 2002 2124 1996 102\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 0 (id = 0)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: None\n", "INFO:tensorflow:tokens: [CLS] here we have the in ##imi ##table charlie chaplin for ##sa ##king his slap ##stick past to tackle the serious subject of anti - semi ##tism , and into ##ler ##ance in general . he portrays two characters - the sweet , innocent jewish barber - a war veteran , and the ravi ##ng and ruthless dictator , aden ##oid h ##yn ##kel . the jewish ghetto in this country is not safe for long , due to the w ##him ##s of h ##yn ##kel and his armed thugs , who routinely rough up its residents , or leave them alone , dependent upon his mood that day or week . the barber is among them , but is befriended by his former commanding officer [SEP]\n", "INFO:tensorflow:input_ids: 101 ************** 1996 1999 27605 10880 4918 23331 ************** 2010 14308 21354 2627 2000 11147 1996 ************** ************** 17456 1010 1998 ************** 1999 ************** 17509 ************** 1996 ************** 3644 13362 1011 1037 ************** 1998 1996 16806 3070 1998 18101 21237 1010 16298 9314 1044 6038 11705 1012 1996 3644 17276 1999 2023 2406 2003 2025 3647 2005 2146 1010 2349 2000 1996 1059 14341 2015 1997 1044 6038 11705 1998 2010 4273 24106 1010 2040 19974 5931 2039 2049 3901 1010 2030 2681 2068 2894 1010 7790 2588 2010 6888 2008 2154 2030 2733 1012 1996 13362 2003 2426 2068 1010 2021 2003 23386 2011 2010 2280 7991 2961 102\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 1 (id = 1)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: None\n", "INFO:tensorflow:tokens: [CLS] i really hated this movie and it ' s the first movie written by step<PERSON> king that i didn ' t finish . i was truly disappointed , it was the worst crap i ' ve ever seen . what were you thinking making three hours out of it ? it may have a quite good story , but actors ? no . suspense ? no . romance ? no . horror ? no . it didn ' t have anything . < br / > < br / > it ' s got this strange , crazy science man with einstein - hair , the classic thing . not real at all . and a man keep getting younger all the time . it seems [SEP]\n", "INFO:tensorflow:input_ids: 101 1045 ************** 3185 1998 ************** 1996 ************** ************** ************** 1005 1056 ************** ************** 1010 ************** 5409 10231 1045 1005 ************** 1012 ************** ************** ************** ************** ************** ************** 1010 ************** 2053 1012 23873 1029 ************** 1029 ************** 1029 ************** 2134 1005 1056 2031 2505 1012 1026 ************** 1026 ************** ************** 2288 2023 4326 1010 4689 2671 2158 2007 15313 1011 2606 1010 1996 4438 2518 1012 2025 2613 2012 2035 1012 1998 1037 2158 2562 2893 3920 2035 1996 2051 1012 2009 3849 102\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 0 (id = 0)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: None\n", "INFO:tensorflow:tokens: [CLS] story chinese tall story tells the story of righteous monk trip ##ita ##ka , who , along with his guardians monkey , sandy and pigs ##y make their journey west on a quest to recover ancient sutra ##s , finally , they reach the final leg of their journey in sha ##che city but all is not as it seems when the city is attacked by evil tree demons . monkey tries his best to battle them but is overwhelmed , knowing his master is in grave danger , he uses his trust ##y golden staff to thrust trip ##ita ##ka to safety . < br / > < br / > the monk ends up being knocked out when he land and when he wakes [SEP]\n", "INFO:tensorflow:input_ids: 101 ************** ************** 2466 1997 19556 ************** ************** 1010 ************** 14240 10608 1010 7525 1998 14695 ************** ************** 1037 ************** 3418 26567 ************** 1010 ************** ************** ************** 21146 ************** ************** ************** 2043 1996 2103 2003 4457 2011 4763 3392 7942 1012 10608 5363 2010 2190 2000 2645 2068 2021 2003 13394 1010 4209 2010 3040 2003 1999 6542 5473 1010 2002 3594 2010 3404 2100 3585 3095 2000 7400 4440 6590 2912 2000 3808 1012 1026 ************** 1026 ************** 1996 8284 4515 2039 2108 6573 2041 2043 2002 2455 1998 2043 2002 17507 102\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 1 (id = 1)\n"], "name": "stdout"}]}, {"metadata": {"id": "ccp5trMwRtmr", "colab_type": "text"}, "cell_type": "markdown", "source": ["#Creating a model\n", "\n", "Now that we've prepared our data, let's focus on building a model. `create_model` does just this below. First, it loads the BERT tf hub module again (this time to extract the computation graph). Next, it creates a single new layer that will be trained to adapt BERT to our sentiment task (i.e. classifying whether a movie review is positive or negative). This strategy of using a mostly trained model is called [fine-tuning](http://wiki.fast.ai/index.php/Fine_tuning)."]}, {"metadata": {"id": "6o2a5ZIvRcJq", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["def create_model(is_predicting, input_ids, input_mask, segment_ids, labels,\n", "                 num_labels):\n", "  \"\"\"Creates a classification model.\"\"\"\n", "\n", "  bert_module = hub.Module(\n", "      BERT_MODEL_HUB,\n", "      trainable=True)\n", "  bert_inputs = dict(\n", "      input_ids=input_ids,\n", "      input_mask=input_mask,\n", "      segment_ids=segment_ids)\n", "  bert_outputs = bert_module(\n", "      inputs=bert_inputs,\n", "      signature=\"tokens\",\n", "      as_dict=True)\n", "\n", "  # Use \"pooled_output\" for classification tasks on an entire sentence.\n", "  # Use \"sequence_outputs\" for token-level output.\n", "  output_layer = bert_outputs[\"pooled_output\"]\n", "\n", "  hidden_size = output_layer.shape[-1].value\n", "\n", "  # Create our own layer to tune for politeness data.\n", "  output_weights = tf.get_variable(\n", "      \"output_weights\", [num_labels, hidden_size],\n", "      initializer=tf.truncated_normal_initializer(stddev=0.02))\n", "\n", "  output_bias = tf.get_variable(\n", "      \"output_bias\", [num_labels], initializer=tf.zeros_initializer())\n", "\n", "  with tf.variable_scope(\"loss\"):\n", "\n", "    # Dropout helps prevent overfitting\n", "    output_layer = tf.nn.dropout(output_layer, keep_prob=0.9)\n", "\n", "    logits = tf.matmul(output_layer, output_weights, transpose_b=True)\n", "    logits = tf.nn.bias_add(logits, output_bias)\n", "    log_probs = tf.nn.log_softmax(logits, axis=-1)\n", "\n", "    # Convert labels into one-hot encoding\n", "    one_hot_labels = tf.one_hot(labels, depth=num_labels, dtype=tf.float32)\n", "\n", "    predicted_labels = tf.squeeze(tf.argmax(log_probs, axis=-1, output_type=tf.int32))\n", "    # If we're predicting, we want predicted labels and the probabiltiies.\n", "    if is_predicting:\n", "      return (predicted_labels, log_probs)\n", "\n", "    # If we're train/eval, compute loss between predicted and actual label\n", "    per_example_loss = -tf.reduce_sum(one_hot_labels * log_probs, axis=-1)\n", "    loss = tf.reduce_mean(per_example_loss)\n", "    return (loss, predicted_labels, log_probs)\n"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "qpE0ZIDOCQzE", "colab_type": "text"}, "cell_type": "markdown", "source": ["Next we'll wrap our model function in a `model_fn_builder` function that adapts our model to work for training, evaluation, and prediction."]}, {"metadata": {"id": "FnH-AnOQ9KKW", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["# model_fn_builder actually creates our model function\n", "# using the passed parameters for num_labels, learning_rate, etc.\n", "def model_fn_builder(num_labels, learning_rate, num_train_steps,\n", "                     num_warmup_steps):\n", "  \"\"\"Returns `model_fn` closure for TPUEstimator.\"\"\"\n", "  def model_fn(features, labels, mode, params):  # pylint: disable=unused-argument\n", "    \"\"\"The `model_fn` for TPUEstimator.\"\"\"\n", "\n", "    input_ids = features[\"input_ids\"]\n", "    input_mask = features[\"input_mask\"]\n", "    segment_ids = features[\"segment_ids\"]\n", "    label_ids = features[\"label_ids\"]\n", "\n", "    is_predicting = (mode == tf.estimator.ModeKeys.PREDICT)\n", "    \n", "    # TRAIN and EVAL\n", "    if not is_predicting:\n", "\n", "      (loss, predicted_labels, log_probs) = create_model(\n", "        is_predicting, input_ids, input_mask, segment_ids, label_ids, num_labels)\n", "\n", "      train_op = bert.optimization.create_optimizer(\n", "          loss, learning_rate, num_train_steps, num_warmup_steps, use_tpu=False)\n", "\n", "      # Calculate evaluation metrics. \n", "      def metric_fn(label_ids, predicted_labels):\n", "        accuracy = tf.metrics.accuracy(label_ids, predicted_labels)\n", "        f1_score = tf.contrib.metrics.f1_score(\n", "            label_ids,\n", "            predicted_labels)\n", "        auc = tf.metrics.auc(\n", "            label_ids,\n", "            predicted_labels)\n", "        recall = tf.metrics.recall(\n", "            label_ids,\n", "            predicted_labels)\n", "        precision = tf.metrics.precision(\n", "            label_ids,\n", "            predicted_labels) \n", "        true_pos = tf.metrics.true_positives(\n", "            label_ids,\n", "            predicted_labels)\n", "        true_neg = tf.metrics.true_negatives(\n", "            label_ids,\n", "            predicted_labels)   \n", "        false_pos = tf.metrics.false_positives(\n", "            label_ids,\n", "            predicted_labels)  \n", "        false_neg = tf.metrics.false_negatives(\n", "            label_ids,\n", "            predicted_labels)\n", "        return {\n", "            \"eval_accuracy\": accuracy,\n", "            \"f1_score\": f1_score,\n", "            \"auc\": auc,\n", "            \"precision\": precision,\n", "            \"recall\": recall,\n", "            \"true_positives\": true_pos,\n", "            \"true_negatives\": true_neg,\n", "            \"false_positives\": false_pos,\n", "            \"false_negatives\": false_neg\n", "        }\n", "\n", "      eval_metrics = metric_fn(label_ids, predicted_labels)\n", "\n", "      if mode == tf.estimator.ModeKeys.TRAIN:\n", "        return tf.estimator.EstimatorSpec(mode=mode,\n", "          loss=loss,\n", "          train_op=train_op)\n", "      else:\n", "          return tf.estimator.EstimatorSpec(mode=mode,\n", "            loss=loss,\n", "            eval_metric_ops=eval_metrics)\n", "    else:\n", "      (predicted_labels, log_probs) = create_model(\n", "        is_predicting, input_ids, input_mask, segment_ids, label_ids, num_labels)\n", "\n", "      predictions = {\n", "          'probabilities': log_probs,\n", "          'labels': predicted_labels\n", "      }\n", "      return tf.estimator.EstimatorSpec(mode, predictions=predictions)\n", "\n", "  # Return the actual model function in the closure\n", "  return model_fn\n"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "OjwJ4bTeWXD8", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["# Compute train and warmup steps from batch size\n", "# These hyperparameters are copied from this colab notebook (https://colab.sandbox.google.com/github/tensorflow/tpu/blob/master/tools/colab/bert_finetuning_with_cloud_tpus.ipynb)\n", "BATCH_SIZE = 32\n", "LEARNING_RATE = 2e-5\n", "NUM_TRAIN_EPOCHS = 3.0\n", "# Warmup is a period of time where hte learning rate \n", "# is small and gradually increases--usually helps training.\n", "WARMUP_PROPORTION = 0.1\n", "# Model configs\n", "SAVE_CHECKPOINTS_STEPS = 500\n", "SAVE_SUMMARY_STEPS = 100"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "emHf9GhfWBZ_", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["# Compute # train and warmup steps from batch size\n", "num_train_steps = int(len(train_features) / BATCH_SIZE * NUM_TRAIN_EPOCHS)\n", "num_warmup_steps = int(num_train_steps * WARMUP_PROPORTION)"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "oEJldMr3WYZa", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["# Specify outpit directory and number of checkpoint steps to save\n", "run_config = tf.estimator.RunConfig(\n", "    model_dir=OUTPUT_DIR,\n", "    save_summary_steps=SAVE_SUMMARY_STEPS,\n", "    save_checkpoints_steps=SAVE_CHECKPOINTS_STEPS)"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "q_WebpS1X97v", "colab_type": "code", "outputId": "1648932a-7391-49d3-8af7-52d514e226e8", "colab": {"base_uri": "https://localhost:8080/", "height": 156}}, "cell_type": "code", "source": ["model_fn = model_fn_builder(\n", "  num_labels=len(label_list),\n", "  learning_rate=LEARNING_RATE,\n", "  num_train_steps=num_train_steps,\n", "  num_warmup_steps=num_warmup_steps)\n", "\n", "estimator = tf.estimator.Estimator(\n", "  model_fn=model_fn,\n", "  config=run_config,\n", "  params={\"batch_size\": BATCH_SIZE})\n"], "execution_count": 55, "outputs": [{"output_type": "stream", "text": ["INFO:tensorflow:Using config: {'_model_dir': 'gs://bert-tfhub/aclImdb_v1', '_tf_random_seed': None, '_save_summary_steps': 100, '_save_checkpoints_steps': 500, '_save_checkpoints_secs': None, '_session_config': allow_soft_placement: true\n", "graph_options {\n", "  rewrite_options {\n", "    meta_optimizer_iterations: ONE\n", "  }\n", "}\n", ", '_keep_checkpoint_max': 5, '_keep_checkpoint_every_n_hours': 10000, '_log_step_count_steps': 100, '_train_distribute': None, '_device_fn': None, '_protocol': None, '_eval_distribute': None, '_experimental_distribute': None, '_service': None, '_cluster_spec': <tensorflow.python.training.server_lib.ClusterSpec object at 0x7fcedb507be0>, '_task_type': 'worker', '_task_id': 0, '_global_id_in_cluster': 0, '_master': '', '_evaluation_master': '', '_is_chief': True, '_num_ps_replicas': 0, '_num_worker_replicas': 1}\n"], "name": "stdout"}]}, {"metadata": {"id": "NOO3RfG1DYLo", "colab_type": "text"}, "cell_type": "markdown", "source": ["Next we create an input builder function that takes our training feature set (`train_features`) and produces a generator. This is a pretty standard design pattern for working with Tensorflow [Estimators](https://www.tensorflow.org/guide/estimators)."]}, {"metadata": {"id": "1Pv2bAlOX_-K", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["# Create an input function for training. drop_remainder = True for using TPUs.\n", "train_input_fn = bert.run_classifier.input_fn_builder(\n", "    features=train_features,\n", "    seq_length=MAX_SEQ_LENGTH,\n", "    is_training=True,\n", "    drop_remainder=False)"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "t6Nukby2EB6-", "colab_type": "text"}, "cell_type": "markdown", "source": ["Now we train our model! For me, using a Colab notebook running on Google's GPUs, my training time was about 14 minutes."]}, {"metadata": {"id": "nucD4gluYJmK", "colab_type": "code", "outputId": "5d728e72-4631-42bf-c48d-3f51d4b968ce", "colab": {"base_uri": "https://localhost:8080/", "height": 68}}, "cell_type": "code", "source": ["print(f'Beginning Training!')\n", "current_time = datetime.now()\n", "estimator.train(input_fn=train_input_fn, max_steps=num_train_steps)\n", "print(\"Training took time \", datetime.now() - current_time)"], "execution_count": 57, "outputs": [{"output_type": "stream", "text": ["Beginning Training!\n", "INFO:tensorflow:Skipping training since max_steps has already saved.\n", "Training took time  0:00:00.759709\n"], "name": "stdout"}]}, {"metadata": {"id": "CmbLTVniARy3", "colab_type": "text"}, "cell_type": "markdown", "source": ["Now let's use our test data to see how well our model did:"]}, {"metadata": {"id": "JIhejfpyJ8Bx", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["test_input_fn = run_classifier.input_fn_builder(\n", "    features=test_features,\n", "    seq_length=MAX_SEQ_LENGTH,\n", "    is_training=False,\n", "    drop_remainder=False)"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "PPVEXhNjYXC-", "colab_type": "code", "outputId": "dd5482cd-c558-465f-c854-ec11a0175316", "colab": {"base_uri": "https://localhost:8080/", "height": 445}}, "cell_type": "code", "source": ["estimator.evaluate(input_fn=test_input_fn, steps=None)"], "execution_count": 59, "outputs": [{"output_type": "stream", "text": ["INFO:tensorflow:Calling model_fn.\n", "INFO:tensorflow:Saver not created because there are no variables in the graph to restore\n"], "name": "stdout"}, {"output_type": "stream", "text": ["/usr/local/lib/python3.6/dist-packages/tensorflow/python/ops/gradients_impl.py:110: UserWarning: Converting sparse IndexedSlices to a dense Tensor of unknown shape. This may consume a large amount of memory.\n", "  \"Converting sparse IndexedSlices to a dense Tensor of unknown shape. \"\n"], "name": "stderr"}, {"output_type": "stream", "text": ["INFO:tensorflow:Done calling model_fn.\n", "INFO:tensorflow:Starting evaluation at 2019-02-12T21:04:20Z\n", "INFO:tensorflow:Graph was finalized.\n", "INFO:tensorflow:Restoring parameters from gs://bert-tfhub/aclImdb_v1/model.ckpt-468\n", "INFO:tensorflow:Running local_init_op.\n", "INFO:tensorflow:Done running local_init_op.\n", "INFO:tensorflow:Finished evaluation at 2019-02-12-21:06:05\n", "INFO:tensorflow:Saving dict for global step 468: auc = 0.86659324, eval_accuracy = 0.8664, f1_score = 0.8659711, false_negatives = 375.0, false_positives = 293.0, global_step = 468, loss = 0.51870537, precision = 0.880457, recall = 0.8519542, true_negatives = 2174.0, true_positives = 2158.0\n", "INFO:tensorflow:Saving 'checkpoint_path' summary for global step 468: gs://bert-tfhub/aclImdb_v1/model.ckpt-468\n"], "name": "stdout"}, {"output_type": "execute_result", "data": {"text/plain": ["{'auc': 0.86659324,\n", " 'eval_accuracy': 0.8664,\n", " 'f1_score': 0.8659711,\n", " 'false_negatives': 375.0,\n", " 'false_positives': 293.0,\n", " 'global_step': 468,\n", " 'loss': 0.51870537,\n", " 'precision': 0.880457,\n", " 'recall': 0.8519542,\n", " 'true_negatives': 2174.0,\n", " 'true_positives': 2158.0}"]}, "metadata": {"tags": []}, "execution_count": 59}]}, {"metadata": {"id": "ueKsULteiz1B", "colab_type": "text"}, "cell_type": "markdown", "source": ["Now let's write code to make predictions on new sentences:"]}, {"metadata": {"id": "OsrbTD2EJTVl", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["def getPrediction(in_sentences):\n", "  labels = [\"Negative\", \"Positive\"]\n", "  input_examples = [run_classifier.InputExample(guid=\"\", text_a = x, text_b = None, label = 0) for x in in_sentences] # here, \"\" is just a dummy label\n", "  input_features = run_classifier.convert_examples_to_features(input_examples, label_list, MAX_SEQ_LENGTH, tokenizer)\n", "  predict_input_fn = run_classifier.input_fn_builder(features=input_features, seq_length=MAX_SEQ_LENGTH, is_training=False, drop_remainder=False)\n", "  predictions = estimator.predict(predict_input_fn)\n", "  return [(sentence, prediction['probabilities'], labels[prediction['labels']]) for sentence, prediction in zip(in_sentences, predictions)]"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "-thbodgih_VJ", "colab_type": "code", "colab": {}}, "cell_type": "code", "source": ["pred_sentences = [\n", "  \"That movie was absolutely awful\",\n", "  \"The acting was a bit lacking\",\n", "  \"The film was creative and surprising\",\n", "  \"Absolutely fantastic!\"\n", "]"], "execution_count": 0, "outputs": []}, {"metadata": {"id": "QrZmvZySKQTm", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 649}, "outputId": "3891fafb-a460-4eb8-fa6c-335a5bbc10e5"}, "cell_type": "code", "source": ["predictions = getPrediction(pred_sentences)"], "execution_count": 72, "outputs": [{"output_type": "stream", "text": ["INFO:tensorflow:Writing example 0 of 4\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: \n", "INFO:tensorflow:tokens: [CLS] that movie was absolutely awful [SEP]\n", "INFO:tensorflow:input_ids: 101 ************** 7078 9643 102 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 0 (id = 0)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: \n", "INFO:tensorflow:tokens: [CLS] the acting was a bit lacking [SEP]\n", "INFO:tensorflow:input_ids: 101 1996 ************** 2978 11158 102 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 0 (id = 0)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: \n", "INFO:tensorflow:tokens: [CLS] the film was creative and surprising [SEP]\n", "INFO:tensorflow:input_ids: 101 1996 ************** 1998 11341 102 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 0 (id = 0)\n", "INFO:tensorflow:*** Example ***\n", "INFO:tensorflow:guid: \n", "INFO:tensorflow:tokens: [CLS] absolutely fantastic ! [SEP]\n", "INFO:tensorflow:input_ids: 101 7078 10392 999 102 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:input_mask: 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:segment_ids: 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", "INFO:tensorflow:label: 0 (id = 0)\n", "INFO:tensorflow:Calling model_fn.\n", "INFO:tensorflow:Saver not created because there are no variables in the graph to restore\n", "INFO:tensorflow:Done calling model_fn.\n", "INFO:tensorflow:Graph was finalized.\n", "INFO:tensorflow:Restoring parameters from gs://bert-tfhub/aclImdb_v1/model.ckpt-468\n", "INFO:tensorflow:Running local_init_op.\n", "INFO:tensorflow:Done running local_init_op.\n"], "name": "stdout"}]}, {"metadata": {"id": "MXkRiEBUqN3n", "colab_type": "text"}, "cell_type": "markdown", "source": ["Voila! We have a sentiment classifier!"]}, {"metadata": {"id": "ERkTE8-7oQLZ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 221}, "outputId": "26c33224-dc2c-4b3d-f7b4-ac3ef0a58b27"}, "cell_type": "code", "source": ["predictions"], "execution_count": 73, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[('That movie was absolutely awful',\n", "  array([-4.9142293e-03, -5.3180690e+00], dtype=float32),\n", "  'Negative'),\n", " ('The acting was a bit lacking',\n", "  array([-0.03325794, -3.4200459 ], dtype=float32),\n", "  'Negative'),\n", " ('The film was creative and surprising',\n", "  array([-5.3589125e+00, -4.7171740e-03], dtype=float32),\n", "  'Positive'),\n", " ('Absolutely fantastic!',\n", "  array([-5.0434084 , -0.00647258], dtype=float32),\n", "  'Positive')]"]}, "metadata": {"tags": []}, "execution_count": 73}]}]}