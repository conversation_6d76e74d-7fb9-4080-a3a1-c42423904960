# 中文情绪分类系统

基于 HuggingFace Transformers 的中文 BERT 情绪分类系统，支持模型训练和 Flask API 服务。

## 功能特性

- ✅ 使用中文 BERT 预训练模型进行情绪分类
- ✅ 支持 7 种情绪类别：anger, disgust, fear, happiness, like, sadness, surprise
- ✅ 自动数据预处理和标签编码
- ✅ 使用 HuggingFace Trainer 进行模型微调
- ✅ Flask API 服务，支持单个和批量预测
- ✅ 完整的测试和验证流程

## 项目结构

```
├── emotion_corpus_microblog.txt    # 原始情绪语料库
├── chinese_L-12_H-768_A-12/        # 中文BERT预训练模型
├── data_loader.py                  # 数据加载与预处理模块
├── train_model.py                  # 模型训练脚本
├── flask_api.py                    # Flask API服务
├── test_system.py                  # 系统测试脚本
├── requirements_sentiment.txt      # Python依赖包
└── README.md                       # 说明文档
```

## 安装依赖

```bash
pip install torch>=1.9.0
pip install transformers>=4.20.0
pip install datasets>=2.0.0
pip install pandas>=1.3.0
pip install numpy>=1.21.0
pip install scikit-learn>=1.0.0
pip install flask>=2.0.0
pip install flask-cors>=3.0.0
pip install tqdm>=4.62.0
pip install matplotlib>=3.5.0
pip install seaborn>=0.11.0
```

或者使用：
```bash
pip install -r requirements_sentiment.txt
```

## 使用步骤

### 1. 数据预处理

首先将原始数据转换为 CSV 格式：

```python
python -c "
import pandas as pd
from collections import Counter

texts = []
labels = []

with open('emotion_corpus_microblog.txt', 'r', encoding='utf-8') as f:
    for line in f:
        line = line.strip()
        if not line:
            continue
        colon_pos = line.find(':')
        if colon_pos == -1:
            continue
        label = line[:colon_pos].strip()
        text = line[colon_pos+1:].strip()
        if text:
            texts.append(text)
            labels.append(label)

df = pd.DataFrame({'text': texts, 'label': labels})
df.to_csv('emotion_data.csv', index=False, encoding='utf-8')
print(f'数据预处理完成，共 {len(df)} 条数据')
print('标签分布:')
print(df['label'].value_counts())
"
```

### 2. 模型训练

运行训练脚本：

```bash
python train_model.py
```

训练参数：
- learning_rate: 2e-5
- batch_size: 16
- num_train_epochs: 3
- weight_decay: 0.01
- max_length: 128

训练完成后，模型将保存到 `./sentiment_model` 目录。

### 3. 启动 API 服务

```bash
python flask_api.py
```

服务将在 `http://localhost:5000` 启动。

### 4. API 使用示例

#### 单个文本预测

```bash
curl -X POST http://localhost:5000/predict \
  -H "Content-Type: application/json" \
  -d '{"text": "今天心情很好，阳光明媚"}'
```

响应示例：
```json
{
  "text": "今天心情很好，阳光明媚",
  "predicted_label": "happiness",
  "confidence": 0.892,
  "all_probabilities": {
    "anger": 0.012,
    "disgust": 0.015,
    "fear": 0.008,
    "happiness": 0.892,
    "like": 0.045,
    "sadness": 0.018,
    "surprise": 0.010
  }
}
```

#### 批量预测

```bash
curl -X POST http://localhost:5000/predict_batch \
  -H "Content-Type: application/json" \
  -d '{"texts": ["今天心情很好", "我很生气"]}'
```

#### 健康检查

```bash
curl http://localhost:5000/health
```

## 测试系统

运行完整的系统测试：

```bash
python test_system.py
```

测试包括：
- 数据加载测试
- 模型训练测试（可选）
- API 服务测试（可选）

## 数据集信息

- **总样本数**: 39,661 条
- **标签类别**: 7 种情绪
- **标签分布**:
  - sadness: 14,052 (35.43%)
  - happiness: 9,959 (25.11%)
  - disgust: 4,876 (12.29%)
  - anger: 4,562 (11.50%)
  - like: 4,540 (11.45%)
  - surprise: 1,011 (2.55%)
  - fear: 661 (1.67%)

## 模型架构

- **基础模型**: 中文 BERT (chinese_L-12_H-768_A-12)
- **分类层**: BertForSequenceClassification
- **最大序列长度**: 128
- **分词器**: BertTokenizer

## API 接口

### POST /predict
单个文本情绪预测

**请求格式**:
```json
{
  "text": "要预测的文本"
}
```

**响应格式**:
```json
{
  "text": "输入文本",
  "predicted_label": "预测标签",
  "confidence": 0.95,
  "all_probabilities": {
    "label1": 0.1,
    "label2": 0.95,
    ...
  }
}
```

### POST /predict_batch
批量文本情绪预测

**请求格式**:
```json
{
  "texts": ["文本1", "文本2", ...]
}
```

### GET /health
健康检查

### GET /
API 信息

## 注意事项

1. **模型加载**: Flask 服务启动时会自动加载模型，只加载一次
2. **内存需求**: BERT 模型较大，建议至少 4GB 内存
3. **GPU 支持**: 自动检测并使用 GPU（如果可用）
4. **批量限制**: 批量预测最多支持 100 个文本
5. **编码格式**: 所有文件使用 UTF-8 编码

## 故障排除

### 模型加载失败
- 检查 `./sentiment_model` 目录是否存在
- 确保模型文件完整
- 检查依赖包版本

### 训练内存不足
- 减小 batch_size
- 使用 gradient_accumulation_steps
- 减少 max_length

### API 服务无响应
- 检查端口 5000 是否被占用
- 查看服务启动日志
- 确认模型已正确加载

## 许可证

MIT License