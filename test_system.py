#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
测试数据加载、模型训练和API服务
"""

import os
import time
import requests
import json
import subprocess
import threading
from data_loader import DataProcessor

def test_data_loading():
    """测试数据加载"""
    print("=" * 50)
    print("测试数据加载...")

    try:
        # 检查CSV文件是否存在
        if not os.path.exists("emotion_data.csv"):
            print("CSV文件不存在，正在生成...")
            # 运行数据预处理
            import pandas as pd
            from collections import Counter

            texts = []
            labels = []

            with open("emotion_corpus_microblog.txt", 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    colon_pos = line.find(':')
                    if colon_pos == -1:
                        continue
                    label = line[:colon_pos].strip()
                    text = line[colon_pos+1:].strip()
                    if text:
                        texts.append(text)
                        labels.append(label)

            df = pd.DataFrame({'text': texts, 'label': labels})
            df.to_csv("emotion_data.csv", index=False, encoding='utf-8')
            print(f"CSV文件已生成，包含 {len(df)} 条数据")

        # 测试数据处理器
        processor = DataProcessor("emotion_data.csv")
        train_dataset, val_dataset, test_dataset = processor.prepare_datasets()

        print(f"✓ 数据加载成功")
        print(f"  训练集: {len(train_dataset)} 样本")
        print(f"  验证集: {len(val_dataset)} 样本")
        print(f"  测试集: {len(test_dataset)} 样本")
        print(f"  标签数量: {processor.num_labels}")
        print(f"  标签名称: {processor.get_label_names()}")

        return True

    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        return False

def test_model_training():
    """测试模型训练（简化版）"""
    print("=" * 50)
    print("测试模型训练...")

    try:
        from train_model import SentimentTrainer

        # 创建训练器
        trainer = SentimentTrainer(
            csv_file="emotion_data.csv",
            model_save_path="./sentiment_model_test",
            pretrained_model_path="chinese_L-12_H-768_A-12"
        )

        # 简化训练（只训练1个epoch用于测试）
        print("开始简化训练（1个epoch）...")
        train_result = trainer.train(
            learning_rate=2e-5,
            batch_size=8,  # 减小batch size
            num_epochs=1,  # 只训练1个epoch
            weight_decay=0.01
        )

        print(f"✓ 模型训练成功")
        print(f"  训练损失: {train_result.training_loss:.4f}")

        # 测试评估
        eval_result, report = trainer.evaluate()
        print(f"  测试准确率: {eval_result['eval_accuracy']:.4f}")

        return True

    except Exception as e:
        print(f"✗ 模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_service():
    """测试API服务"""
    print("=" * 50)
    print("测试API服务...")

    # 检查模型是否存在
    model_path = "./sentiment_model"
    if not os.path.exists(model_path):
        print("模型不存在，使用测试模型...")
        model_path = "./sentiment_model_test"
        if not os.path.exists(model_path):
            print("✗ 没有可用的模型进行API测试")
            return False

    try:
        # 启动Flask服务（在后台）
        print("启动Flask API服务...")

        # 修改flask_api.py中的模型路径
        with open("flask_api.py", "r", encoding="utf-8") as f:
            content = f.read()

        # 临时修改模型路径
        content_modified = content.replace(
            'def __init__(self, model_path="./sentiment_model"):',
            f'def __init__(self, model_path="{model_path}"):'
        )

        with open("flask_api_test.py", "w", encoding="utf-8") as f:
            f.write(content_modified)

        # 启动服务
        process = subprocess.Popen(
            ["python", "flask_api_test.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        # 等待服务启动
        print("等待服务启动...")
        time.sleep(10)

        # 测试API
        base_url = "http://localhost:5000"

        # 测试健康检查
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✓ 健康检查通过")
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
            return False

        # 测试预测接口
        test_texts = [
            "今天心情很好，阳光明媚",
            "我很生气，这太不公平了",
            "感到很害怕，不知道该怎么办",
            "这个消息让我很惊讶"
        ]

        for text in test_texts:
            response = requests.post(
                f"{base_url}/predict",
                json={"text": text},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                print(f"✓ 预测成功: '{text}' -> {result['predicted_label']} ({result['confidence']:.3f})")
            else:
                print(f"✗ 预测失败: {response.status_code}")

        # 测试批量预测
        response = requests.post(
            f"{base_url}/predict_batch",
            json={"texts": test_texts[:2]},
            timeout=15
        )

        if response.status_code == 200:
            print("✓ 批量预测成功")
        else:
            print(f"✗ 批量预测失败: {response.status_code}")

        # 关闭服务
        process.terminate()
        process.wait()

        # 清理临时文件
        if os.path.exists("flask_api_test.py"):
            os.remove("flask_api_test.py")

        print("✓ API服务测试完成")
        return True

    except Exception as e:
        print(f"✗ API服务测试失败: {e}")
        # 确保进程被关闭
        try:
            process.terminate()
        except:
            pass
        return False

def main():
    """主测试函数"""
    print("开始系统测试...")
    print("注意: 这是一个完整的系统测试，可能需要较长时间")

    results = []

    # 测试数据加载
    results.append(("数据加载", test_data_loading()))

    # 测试模型训练
    print("\n是否进行模型训练测试？(y/n): ", end="")
    if input().lower() == 'y':
        results.append(("模型训练", test_model_training()))
    else:
        print("跳过模型训练测试")
        results.append(("模型训练", None))

    # 测试API服务
    print("\n是否进行API服务测试？(y/n): ", end="")
    if input().lower() == 'y':
        results.append(("API服务", test_api_service()))
    else:
        print("跳过API服务测试")
        results.append(("API服务", None))

    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    for name, result in results:
        if result is True:
            print(f"✓ {name}: 通过")
        elif result is False:
            print(f"✗ {name}: 失败")
        else:
            print(f"- {name}: 跳过")

    print("\n测试完成!")

if __name__ == "__main__":
    main()