#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型训练脚本
使用HuggingFace Transformers和Trainer进行BERT情绪分类模型训练
"""

import os
import torch
import numpy as np
from transformers import (
    BertForSequenceClassification,
    BertTokenizer,
    Trainer,
    TrainingArguments,
    EarlyStoppingCallback
)
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, classification_report
import json
from data_loader import DataProcessor

class SentimentTrainer:
    """情绪分类模型训练器"""

    def __init__(self, csv_file, model_save_path="./sentiment_model",
                 pretrained_model_path="chinese_L-12_H-768_A-12"):
        self.csv_file = csv_file
        self.model_save_path = model_save_path
        self.pretrained_model_path = pretrained_model_path
        self.processor = DataProcessor(csv_file)
        self.model = None
        self.tokenizer = None
        self.trainer = None

    def setup_model(self):
        """设置模型和分词器"""
        print("正在设置模型和分词器...")

        # 准备数据集以获取标签数量
        train_dataset, val_dataset, test_dataset = self.processor.prepare_datasets()
        num_labels = self.processor.num_labels

        print(f"标签数量: {num_labels}")
        print(f"标签名称: {self.processor.get_label_names()}")

        # 加载预训练模型
        try:
            if os.path.exists(self.pretrained_model_path):
                print(f"从本地加载预训练模型: {self.pretrained_model_path}")
                self.model = BertForSequenceClassification.from_pretrained(
                    self.pretrained_model_path,
                    num_labels=num_labels,
                    local_files_only=True
                )
                self.tokenizer = self.processor.tokenizer
            else:
                print("从在线加载预训练模型: bert-base-chinese")
                self.model = BertForSequenceClassification.from_pretrained(
                    "bert-base-chinese",
                    num_labels=num_labels
                )
                self.tokenizer = BertTokenizer.from_pretrained("bert-base-chinese")
        except Exception as e:
            print(f"加载模型失败: {e}")
            print("使用备用在线模型")
            self.model = BertForSequenceClassification.from_pretrained(
                "bert-base-chinese",
                num_labels=num_labels
            )
            self.tokenizer = BertTokenizer.from_pretrained("bert-base-chinese")

        return train_dataset, val_dataset, test_dataset

    def compute_metrics(self, eval_pred):
        """计算评估指标"""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)

        precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='weighted')
        acc = accuracy_score(labels, predictions)

        return {
            'accuracy': acc,
            'f1': f1,
            'precision': precision,
            'recall': recall
        }

    def train(self, learning_rate=2e-5, batch_size=16, num_epochs=3, weight_decay=0.01):
        """训练模型"""
        print("开始训练模型...")

        # 设置模型和数据
        train_dataset, val_dataset, test_dataset = self.setup_model()

        # 创建输出目录
        os.makedirs(self.model_save_path, exist_ok=True)

        # 训练参数
        training_args = TrainingArguments(
            output_dir=self.model_save_path,
            num_train_epochs=num_epochs,
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            warmup_steps=500,
            weight_decay=weight_decay,
            learning_rate=learning_rate,
            logging_dir=f'{self.model_save_path}/logs',
            logging_steps=100,
            evaluation_strategy="epoch",
            save_strategy="epoch",
            load_best_model_at_end=True,
            metric_for_best_model="f1",
            greater_is_better=True,
            save_total_limit=2,
            report_to=None,  # 禁用wandb等
        )

        # 创建Trainer
        self.trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            compute_metrics=self.compute_metrics,
            callbacks=[EarlyStoppingCallback(early_stopping_patience=2)]
        )

        # 开始训练
        print("训练开始...")
        train_result = self.trainer.train()

        # 保存训练结果
        print("训练完成，正在保存模型...")
        self.trainer.save_model()
        self.tokenizer.save_pretrained(self.model_save_path)

        # 保存标签映射
        label_mapping = {
            'label_names': self.processor.get_label_names(),
            'num_labels': self.processor.num_labels,
            'label_encoder_classes': self.processor.label_encoder.classes_.tolist()
        }

        with open(f"{self.model_save_path}/label_mapping.json", "w", encoding="utf-8") as f:
            json.dump(label_mapping, f, ensure_ascii=False, indent=2)

        print(f"模型已保存到: {self.model_save_path}")

        return train_result

    def evaluate(self, test_dataset=None):
        """评估模型"""
        if test_dataset is None:
            _, _, test_dataset = self.processor.prepare_datasets()

        print("正在评估模型...")
        eval_result = self.trainer.evaluate(test_dataset)

        # 获取详细的分类报告
        predictions = self.trainer.predict(test_dataset)
        y_pred = np.argmax(predictions.predictions, axis=1)
        y_true = predictions.label_ids

        # 生成分类报告
        label_names = self.processor.get_label_names()
        report = classification_report(y_true, y_pred, target_names=label_names, output_dict=True)

        print("\n分类报告:")
        print(classification_report(y_true, y_pred, target_names=label_names))

        # 保存评估结果
        eval_results = {
            'eval_metrics': eval_result,
            'classification_report': report
        }

        with open(f"{self.model_save_path}/eval_results.json", "w", encoding="utf-8") as f:
            json.dump(eval_results, f, ensure_ascii=False, indent=2)

        return eval_result, report

def main():
    """主函数"""
    print("开始训练中文情绪分类模型...")

    # 检查数据文件
    csv_file = "emotion_data.csv"
    if not os.path.exists(csv_file):
        print(f"错误: 数据文件 {csv_file} 不存在")
        print("请先运行数据预处理脚本生成CSV文件")
        return

    # 创建训练器
    trainer = SentimentTrainer(
        csv_file=csv_file,
        model_save_path="./sentiment_model",
        pretrained_model_path="chinese_L-12_H-768_A-12"
    )

    # 训练模型
    try:
        train_result = trainer.train(
            learning_rate=2e-5,
            batch_size=16,
            num_epochs=3,
            weight_decay=0.01
        )

        print(f"\n训练完成!")
        print(f"训练损失: {train_result.training_loss:.4f}")

        # 评估模型
        eval_result, report = trainer.evaluate()
        print(f"\n评估完成!")
        print(f"测试准确率: {eval_result['eval_accuracy']:.4f}")
        print(f"测试F1分数: {eval_result['eval_f1']:.4f}")

    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()